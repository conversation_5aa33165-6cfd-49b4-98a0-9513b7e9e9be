import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common'; // ValidationPipe için import
import { PrismaExceptionFilter } from './common/filters/prisma-exception.filter'; // Hata filtremizi import et

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Global Validation Pipe'ı etkinleştir
  // Bu, DTO'lardaki @IsString, @IsNotEmpty gibi validasyonların çalışmasını sağlar.
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true, // DTO'da tanımlanmayan property'leri otomatik olarak kaldırır
    forbidNonWhitelisted: true, // DTO'da tanımlanmayan property'ler geldiğinde hata fırlatır
    transform: true, // Gelen payload'ı DTO instance'ına dönüştürür (parametre dönüşümlerini sağlar)
  }));

  // Global hata filtremizi uygula
  app.useGlobalFilters(new PrismaExceptionFilter());

  await app.listen(3000);
}
bootstrap();
