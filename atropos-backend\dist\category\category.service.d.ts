import { PrismaService } from '../prisma/prisma.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
export declare class CategoryService {
    private prisma;
    constructor(prisma: PrismaService);
    createCategory(data: CreateCategoryDto): Promise<{
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
    findAllCategories(companyId?: string, parentId?: string): Promise<({
        parent: {
            id: string;
            name: string;
        } | null;
        children: {
            id: string;
            name: string;
        }[];
    } & {
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    })[]>;
    findOneCategory(id: string): Promise<{
        parent: {
            id: string;
            name: string;
        } | null;
        children: {
            id: string;
            name: string;
        }[];
    } & {
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
    updateCategory(id: string, data: UpdateCategoryDto): Promise<{
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
    removeCategory(id: string): Promise<{
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
}
