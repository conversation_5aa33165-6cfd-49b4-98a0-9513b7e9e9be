// src/user/dto/update-user.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateUserDto } from './create-user.dto';
import { IsString, IsOptional, MinLength } from 'class-validator'; // <PERSON><PERSON> validatorları

export class UpdateUserDto extends PartialType(CreateUserDto) {
    @IsString()
    @IsOptional()
    @MinLength(8) // Şifre güncellenirken minimum uzunluk
    password?: string; // Ş<PERSON>renin güncellenebilir olması ama opsiyonel olması
}
