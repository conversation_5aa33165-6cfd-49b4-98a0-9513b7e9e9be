import { TaxService } from './tax.service';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
export declare class TaxController {
    private readonly taxService;
    constructor(taxService: TaxService);
    create(createTaxDto: CreateTaxDto): Promise<{
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    findAll(companyId?: string): Promise<({
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    update(id: string, updateTaxDto: UpdateTaxDto): Promise<{
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
}
