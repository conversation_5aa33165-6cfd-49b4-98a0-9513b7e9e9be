import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
export declare class CategoryController {
    private readonly categoryService;
    constructor(categoryService: CategoryService);
    create(createCategoryDto: CreateCategoryDto): Promise<{
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
    findAll(companyId?: string, parentId?: string): Promise<({
        parent: {
            id: string;
            name: string;
        } | null;
        children: {
            id: string;
            name: string;
        }[];
    } & {
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    })[]>;
    findOne(id: string): Promise<{
        parent: {
            id: string;
            name: string;
        } | null;
        children: {
            id: string;
            name: string;
        }[];
    } & {
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
    update(id: string, updateCategoryDto: UpdateCategoryDto): Promise<{
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        companyId: string;
        parentId: string | null;
        name: string;
        description: string | null;
        image: string | null;
        color: string | null;
        icon: string | null;
        showInKitchen: boolean;
        preparationTime: number | null;
        displayOrder: number;
        active: boolean;
        showInMenu: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        printerGroupId: string | null;
    }>;
}
