import { PrismaService } from '../prisma/prisma.service';
import { CreateTaxDto } from './dto/create-tax.dto';
import { UpdateTaxDto } from './dto/update-tax.dto';
export declare class TaxService {
    private prisma;
    constructor(prisma: PrismaService);
    createTax(data: CreateTaxDto): Promise<{
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    findAllTaxes(companyId?: string): Promise<({
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    })[]>;
    findOneTax(id: string): Promise<{
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateTax(id: string, data: UpdateTaxDto): Promise<{
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    removeTax(id: string): Promise<{
        id: string;
        companyId: string;
        name: string;
        rate: import("generated/prisma/runtime/library").Decimal;
        code: string;
        type: import("generated/prisma").$Enums.TaxType;
        isDefault: boolean;
        isIncluded: boolean;
        active: boolean;
        version: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
}
