// src/app.module.ts
import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { CompanyModule } from './company/company.module';
import { BranchModule } from './branch/branch.module'; // Bu satırı ekle
import { UserModule } from './user/user.module'; // Bu satırı ekle

@Module({
  imports: [PrismaModule, CompanyModule, BranchModule, UserModule], // Buraya UserModule'ü ekle
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}